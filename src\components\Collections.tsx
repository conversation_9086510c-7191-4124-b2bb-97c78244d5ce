import React, { useState, useEffect } from 'react'
import { X, Edit3, Trash2, FolderPlus, Folder, Image as ImageIcon, Save } from 'lucide-react'
import { ImageCollection, GeneratedImage } from '../types'

interface CollectionsProps {
  isOpen: boolean
  onClose: () => void
  images: GeneratedImage[]
  collections: ImageCollection[]
  onCreateCollection: (collection: Omit<ImageCollection, 'id' | 'createdAt' | 'updatedAt'>) => void
  onUpdateCollection: (id: string, updates: Partial<ImageCollection>) => void
  onDeleteCollection: (id: string) => void
  onAddImageToCollection: (collectionId: string, imageId: string) => void
  onRemoveImageFromCollection: (collectionId: string, imageId: string) => void
  selectedImages?: Set<string>
  onSelectCollection?: (collection: ImageCollection) => void
}

const COLLECTION_COLORS = [
  '#3b82f6', // blue
  '#ef4444', // red
  '#10b981', // green
  '#f59e0b', // yellow
  '#8b5cf6', // purple
  '#ec4899', // pink
  '#06b6d4', // cyan
  '#84cc16', // lime
  '#f97316', // orange
  '#6366f1', // indigo
]

const Collections: React.FC<CollectionsProps> = ({
  isOpen,
  onClose,
  images,
  collections,
  onCreateCollection,
  onUpdateCollection,
  onDeleteCollection,
  onAddImageToCollection,
  onRemoveImageFromCollection,
  selectedImages = new Set(),
  onSelectCollection
}) => {
  const [isCreating, setIsCreating] = useState(false)
  const [editingCollection, setEditingCollection] = useState<ImageCollection | null>(null)
  const [newCollectionName, setNewCollectionName] = useState('')
  const [newCollectionDescription, setNewCollectionDescription] = useState('')
  const [newCollectionColor, setNewCollectionColor] = useState(COLLECTION_COLORS[0])
  const [selectedCollectionId, setSelectedCollectionId] = useState<string | null>(null)

  // Close on Escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (isCreating || editingCollection) {
          handleCancelEdit()
        } else {
          onClose()
        }
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, isCreating, editingCollection, onClose])

  const handleCreateCollection = () => {
    if (!newCollectionName.trim()) return

    onCreateCollection({
      name: newCollectionName.trim(),
      description: newCollectionDescription.trim() || undefined,
      imageIds: Array.from(selectedImages),
      color: newCollectionColor,
    })

    handleCancelEdit()
  }

  const handleUpdateCollection = () => {
    if (!editingCollection || !newCollectionName.trim()) return

    onUpdateCollection(editingCollection.id, {
      name: newCollectionName.trim(),
      description: newCollectionDescription.trim() || undefined,
      color: newCollectionColor,
      updatedAt: Date.now(),
    })

    handleCancelEdit()
  }

  const handleCancelEdit = () => {
    setIsCreating(false)
    setEditingCollection(null)
    setNewCollectionName('')
    setNewCollectionDescription('')
    setNewCollectionColor(COLLECTION_COLORS[0])
  }

  const handleStartEdit = (collection: ImageCollection) => {
    setEditingCollection(collection)
    setNewCollectionName(collection.name)
    setNewCollectionDescription(collection.description || '')
    setNewCollectionColor(collection.color || COLLECTION_COLORS[0])
  }

  const handleAddSelectedImages = (collectionId: string) => {
    selectedImages.forEach(imageId => {
      onAddImageToCollection(collectionId, imageId)
    })
  }

  const getCollectionImages = (collection: ImageCollection) => {
    return images.filter(img => collection.imageIds.includes(img.id))
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-lg w-full h-full max-w-6xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <Folder className="w-6 h-6 text-accent-primary" />
            <h2 className="text-2xl font-semibold text-white">Collections</h2>
            <span className="text-sm text-gray-400">
              ({collections.length} collection{collections.length !== 1 ? 's' : ''})
            </span>
          </div>
          
          <div className="flex items-center gap-3">
            <button
              onClick={() => setIsCreating(true)}
              className="flex items-center gap-2 px-4 py-2 bg-accent-primary hover:bg-accent-secondary text-white rounded-lg transition-colors"
            >
              <FolderPlus className="w-4 h-4" />
              New Collection
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Collections List */}
          <div className="w-1/3 border-r border-gray-700 flex flex-col">
            <div className="p-4 border-b border-gray-700">
              <h3 className="text-lg font-medium text-white mb-2">Your Collections</h3>
              {selectedImages.size > 0 && (
                <p className="text-sm text-gray-400">
                  {selectedImages.size} image{selectedImages.size !== 1 ? 's' : ''} selected
                </p>
              )}
            </div>
            
            <div className="flex-1 overflow-y-auto p-4 space-y-3">
              {collections.map((collection) => {
                const collectionImages = getCollectionImages(collection)
                const isSelected = selectedCollectionId === collection.id
                
                return (
                  <div
                    key={collection.id}
                    className={`p-4 rounded-lg border cursor-pointer transition-all ${
                      isSelected
                        ? 'border-accent-primary bg-accent-primary/10'
                        : 'border-gray-700 hover:border-gray-600 bg-gray-800/50'
                    }`}
                    onClick={() => {
                      setSelectedCollectionId(collection.id)
                      onSelectCollection?.(collection)
                    }}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: collection.color || COLLECTION_COLORS[0] }}
                        />
                        <h4 className="font-medium text-white">{collection.name}</h4>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleStartEdit(collection)
                          }}
                          className="p-1 text-gray-400 hover:text-white transition-colors"
                          title="Edit collection"
                        >
                          <Edit3 className="w-4 h-4" />
                        </button>
                        {!collection.isDefault && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              onDeleteCollection(collection.id)
                            }}
                            className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                            title="Delete collection"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    </div>
                    
                    {collection.description && (
                      <p className="text-sm text-gray-400 mb-2">{collection.description}</p>
                    )}
                    
                    <div className="flex items-center justify-between text-sm text-gray-400">
                      <span className="flex items-center gap-1">
                        <ImageIcon className="w-4 h-4" />
                        {collectionImages.length} image{collectionImages.length !== 1 ? 's' : ''}
                      </span>
                      
                      {selectedImages.size > 0 && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleAddSelectedImages(collection.id)
                          }}
                          className="px-2 py-1 text-xs bg-accent-primary hover:bg-accent-secondary text-white rounded transition-colors"
                        >
                          + Add {selectedImages.size}
                        </button>
                      )}
                    </div>
                  </div>
                )
              })}
              
              {collections.length === 0 && (
                <div className="text-center py-8">
                  <Folder className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                  <p className="text-gray-400">No collections yet</p>
                  <p className="text-sm text-gray-500">Create your first collection to organize images</p>
                </div>
              )}
            </div>
          </div>

          {/* Collection Details/Create Form */}
          <div className="flex-1 flex flex-col">
            {(isCreating || editingCollection) ? (
              <div className="p-6">
                <h3 className="text-lg font-medium text-white mb-4">
                  {isCreating ? 'Create New Collection' : 'Edit Collection'}
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Collection Name
                    </label>
                    <input
                      type="text"
                      value={newCollectionName}
                      onChange={(e) => setNewCollectionName(e.target.value)}
                      placeholder="Enter collection name..."
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none"
                      autoFocus
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Description (Optional)
                    </label>
                    <textarea
                      value={newCollectionDescription}
                      onChange={(e) => setNewCollectionDescription(e.target.value)}
                      placeholder="Enter collection description..."
                      rows={3}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none resize-none"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Color
                    </label>
                    <div className="flex items-center gap-2">
                      {COLLECTION_COLORS.map((color) => (
                        <button
                          key={color}
                          onClick={() => setNewCollectionColor(color)}
                          className={`w-8 h-8 rounded-full border-2 transition-all ${
                            newCollectionColor === color
                              ? 'border-white scale-110'
                              : 'border-gray-600 hover:border-gray-400'
                          }`}
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                  </div>
                  
                  {isCreating && selectedImages.size > 0 && (
                    <div className="p-3 bg-gray-800 rounded-lg">
                      <p className="text-sm text-gray-300">
                        {selectedImages.size} selected image{selectedImages.size !== 1 ? 's' : ''} will be added to this collection
                      </p>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center gap-3 mt-6">
                  <button
                    onClick={isCreating ? handleCreateCollection : handleUpdateCollection}
                    disabled={!newCollectionName.trim()}
                    className="flex items-center gap-2 px-4 py-2 bg-accent-primary hover:bg-accent-secondary disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                  >
                    <Save className="w-4 h-4" />
                    {isCreating ? 'Create Collection' : 'Save Changes'}
                  </button>
                  <button
                    onClick={handleCancelEdit}
                    className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : selectedCollectionId ? (
              <div className="flex-1 p-6">
                {(() => {
                  const collection = collections.find(c => c.id === selectedCollectionId)
                  if (!collection) return null
                  
                  const collectionImages = getCollectionImages(collection)
                  
                  return (
                    <div>
                      <div className="flex items-center gap-3 mb-6">
                        <div
                          className="w-6 h-6 rounded-full"
                          style={{ backgroundColor: collection.color || COLLECTION_COLORS[0] }}
                        />
                        <h3 className="text-xl font-semibold text-white">{collection.name}</h3>
                        <span className="text-sm text-gray-400">
                          ({collectionImages.length} image{collectionImages.length !== 1 ? 's' : ''})
                        </span>
                      </div>
                      
                      {collection.description && (
                        <p className="text-gray-300 mb-6">{collection.description}</p>
                      )}
                      
                      {collectionImages.length > 0 ? (
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                          {collectionImages.map((image) => (
                            <div key={image.id} className="relative group">
                              <img
                                src={image.url}
                                alt={image.prompt}
                                className="w-full aspect-square object-cover rounded-lg"
                              />
                              <button
                                onClick={() => onRemoveImageFromCollection(collection.id, image.id)}
                                className="absolute top-2 right-2 p-1 bg-red-600 hover:bg-red-700 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                                title="Remove from collection"
                              >
                                <X className="w-4 h-4" />
                              </button>
                              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2 rounded-b-lg">
                                <p className="text-xs text-white truncate">{image.prompt}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <ImageIcon className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                          <p className="text-gray-400 mb-2">This collection is empty</p>
                          <p className="text-sm text-gray-500">
                            Select images from your gallery and add them to this collection
                          </p>
                        </div>
                      )}
                    </div>
                  )
                })()}
              </div>
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <Folder className="w-20 h-20 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">Select a Collection</h3>
                  <p className="text-gray-400">
                    Choose a collection from the left to view its contents
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Collections
