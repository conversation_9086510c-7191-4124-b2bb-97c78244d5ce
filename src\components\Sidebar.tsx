import React from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, AlertCircle, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Eye, EyeOff } from 'lucide-react'
import { AppSettings, AspectRatio, Resolution, AIService, PollinationsModel } from '../types'
import { IMAGEFX_MODELS } from '../utils/imagefx'

interface SidebarProps {
  settings: AppSettings
  onSettingsChange: (settings: AppSettings) => void
  onGenerate: (prompt: string) => void
  isGenerating: boolean
}

const aspectRatioOptions: { value: AspectRatio; label: string }[] = [
  { value: 'square', label: 'Square (1:1)' },
  { value: 'portrait', label: 'Portrait (3:4)' },
  { value: 'portrait_3_4', label: 'Portrait (3:4) - ImageFX' },
  { value: 'landscape', label: 'Landscape (4:3)' },
  { value: 'landscape_4_3', label: 'Landscape (4:3) - ImageFX' },
]

const resolutionOptions: { value: Resolution; label: string }[] = [
  { value: '512x512', label: '512 × 512' },
  { value: '768x768', label: '768 × 768' },
  { value: '1024x1024', label: '1024 × 1024' },
  { value: '1024x768', label: '1024 × 768' },
  { value: '768x1024', label: '768 × 1024' },
  { value: '1536x1024', label: '1536 × 1024' },
  { value: '1024x1536', label: '1024 × 1536' },
]

const pollinationsModelOptions: { value: PollinationsModel; label: string; description: string }[] = [
  { value: 'flux', label: 'Flux', description: 'High-quality, balanced model' },
  { value: 'turbo', label: 'Turbo', description: 'Fast generation, good quality' },
  { value: 'flux-realism', label: 'Flux Realism', description: 'Photorealistic images' },
  { value: 'flux-cablyai', label: 'Flux CablyAI', description: 'Artistic and creative' },
  { value: 'flux-anime', label: 'Flux Anime', description: 'Anime and manga style' },
  { value: 'flux-3d', label: 'Flux 3D', description: '3D rendered style' },
  { value: 'any-dark', label: 'Any Dark', description: 'Dark themed images' },
]

const aiServiceOptions: { value: AIService; label: string; description: string }[] = [
  {
    value: 'pollinations',
    label: 'Pollinations AI',
    description: 'Free, fast, no auth required'
  },
  {
    value: 'imagefx',
    label: 'ImageFX',
    description: 'Google\'s ImageFX (requires auth)'
  },
  {
    value: 'mage',
    label: 'Mage.space (Experimental)',
    description: 'Web scraping - requires authentication'
  },
]

const Sidebar: React.FC<SidebarProps> = ({
  settings,
  onSettingsChange,
  onGenerate,
  isGenerating,
}) => {
  const prompt = settings.prompt || ''

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (prompt.trim() && !isGenerating) {
      onGenerate(prompt.trim())
    }
  }

  const handlePromptChange = (value: string) => {
    updateSetting('prompt', value)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault()
      handleSubmit(e as any)
    }
  }

  const updateSetting = <K extends keyof AppSettings>(
    key: K,
    value: AppSettings[K]
  ) => {
    onSettingsChange({ ...settings, [key]: value })
  }

  return (
    <div className="w-80 bg-sidebar border-r border-matte flex flex-col relative">
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-purple-950/10 via-transparent to-transparent pointer-events-none" />

      {/* Header */}
      <div className="relative p-6 border-b border-matte/50">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 rounded-xl bg-gradient-to-br from-accent-primary to-accent-muted shadow-glow-sm">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-white tracking-tight">
              Imagen AI
            </h1>
            <p className="text-xs text-gray-400 font-medium">AI Image Generator</p>
          </div>
        </div>
      </div>

      {/* Prompt Section */}
      <div className="relative p-6 border-b border-matte/50">
        <form onSubmit={handleSubmit} className="space-y-5">
          <div>
            <label className="text-sm font-semibold text-gray-200 mb-3 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full" />
                Describe your image
              </div>
              <span className="text-xs text-gray-500 font-normal">Ctrl+Enter to generate</span>
            </label>
            <div className="relative">
              <textarea
                value={prompt}
                onChange={(e) => handlePromptChange(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="A beautiful landscape with mountains and a lake at sunset..."
                className="input-primary w-full h-28 resize-none text-sm leading-relaxed"
                disabled={isGenerating}
              />
              <div className="absolute inset-0 rounded-xl pointer-events-none bg-gradient-to-r from-transparent via-transparent to-accent-primary/5" />
            </div>
            <div className="flex justify-between items-center mt-2">
              <div className="text-xs text-gray-500">
                {prompt.length > 0 && `${prompt.length} characters`}
              </div>
              <div className="text-xs text-gray-500">
                {prompt.length > 500 && 'Consider shortening for better results'}
              </div>
            </div>
          </div>

          <button
            type="submit"
            disabled={!prompt.trim() || isGenerating}
            className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-3 text-sm font-semibold"
          >
            {isGenerating ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Sparkles className="w-4 h-4" />
                Generate Images
              </>
            )}
          </button>
        </form>
      </div>

      {/* Settings Section */}
      <div className="relative p-6 flex-1 overflow-y-auto custom-scrollbar">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-1.5 rounded-lg bg-gradient-to-br from-accent-primary/20 to-accent-muted/20 border border-accent-primary/30">
            <Settings className="w-4 h-4 text-accent-primary" />
          </div>
          <h2 className="text-lg font-bold text-white tracking-tight">Settings</h2>
        </div>

        <div className="space-y-8">
          {/* AI Service */}
          <div>
            <label className="text-sm font-semibold text-gray-200 mb-4 flex items-center gap-2">
              <div className="w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full" />
              AI Service
            </label>
            <div className="space-y-3">
              {aiServiceOptions.map((option) => (
                <label
                  key={option.value}
                  className="group flex items-start gap-4 p-4 rounded-xl border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300 hover:bg-card/50 backdrop-blur-sm"
                >
                  <div className="relative mt-0.5">
                    <input
                      type="radio"
                      name="aiService"
                      value={option.value}
                      checked={settings.aiService === option.value}
                      onChange={(e) => updateSetting('aiService', e.target.value as AIService)}
                      className="sr-only"
                    />
                    <div className={`w-4 h-4 rounded-full border-2 transition-all duration-300 ${
                      settings.aiService === option.value
                        ? 'border-accent-primary bg-accent-primary shadow-glow-sm'
                        : 'border-gray-500 group-hover:border-accent-primary/70'
                    }`}>
                      {settings.aiService === option.value && (
                        <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                      )}
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-semibold text-white group-hover:text-accent-secondary transition-colors">
                      {option.label}
                    </div>
                    <div className="text-xs text-gray-400 mt-1 leading-relaxed">
                      {option.description}
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Aspect Ratio */}
          <div>
            <label className="text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2">
              <div className="w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full" />
              Aspect Ratio
            </label>
            <div className="relative">
              <select
                value={settings.aspectRatio}
                onChange={(e) => updateSetting('aspectRatio', e.target.value as AspectRatio)}
                className="input-primary w-full appearance-none cursor-pointer"
              >
                {aspectRatioOptions.map((option) => (
                  <option key={option.value} value={option.value} className="bg-input text-white">
                    {option.label}
                  </option>
                ))}
              </select>
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>

          {/* Resolution */}
          <div>
            <label className="text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2">
              <div className="w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full" />
              Resolution
            </label>
            <div className="relative">
              <select
                value={settings.resolution}
                onChange={(e) => updateSetting('resolution', e.target.value as Resolution)}
                className="input-primary w-full appearance-none cursor-pointer"
              >
                {resolutionOptions.map((option) => (
                  <option key={option.value} value={option.value} className="bg-input text-white">
                    {option.label}
                  </option>
                ))}
              </select>
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>

          {/* Pollinations Settings (if selected) */}
          {settings.aiService === 'pollinations' && (
            <div className="space-y-6">
              <div>
                <label className="text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2">
                  <div className="w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full" />
                  Pollinations Model
                </label>
                <div className="space-y-2">
                  {pollinationsModelOptions.map((option) => (
                    <label
                      key={option.value}
                      className="group flex items-start gap-4 p-3 rounded-xl border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300 hover:bg-card/50 backdrop-blur-sm"
                    >
                      <div className="relative mt-0.5">
                        <input
                          type="radio"
                          name="pollinationsModel"
                          value={option.value}
                          checked={settings.pollinationsModel === option.value}
                          onChange={(e) => updateSetting('pollinationsModel', e.target.value as PollinationsModel)}
                          className="sr-only"
                        />
                        <div className={`w-4 h-4 rounded-full border-2 transition-all duration-300 ${
                          settings.pollinationsModel === option.value
                            ? 'border-accent-primary bg-accent-primary shadow-glow-sm'
                            : 'border-gray-500 group-hover:border-accent-primary/70'
                        }`}>
                          {settings.pollinationsModel === option.value && (
                            <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                          )}
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-semibold text-white group-hover:text-accent-secondary transition-colors">
                          {option.label}
                        </div>
                        <div className="text-xs text-gray-400 mt-1 leading-relaxed">
                          {option.description}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Advanced Pollinations Settings */}
              <div>
                <label className="text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2">
                  <div className="w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full" />
                  Advanced Options
                </label>
                <div className="space-y-4">
                  {/* Seed */}
                  <div>
                    <label className="text-xs font-medium text-gray-300 mb-2 flex items-center gap-2">
                      <Shuffle className="w-3 h-3" />
                      Seed (optional)
                    </label>
                    <input
                      type="number"
                      value={settings.pollinationsSeed || ''}
                      onChange={(e) => updateSetting('pollinationsSeed', e.target.value ? parseInt(e.target.value) : undefined)}
                      placeholder="Random seed for reproducible results"
                      className="input-primary w-full text-sm"
                      min="0"
                      max="999999"
                    />
                  </div>

                  {/* Toggle Options */}
                  <div className="grid grid-cols-2 gap-3">
                    <label className="flex items-center gap-2 p-3 rounded-lg border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300">
                      <input
                        type="checkbox"
                        checked={settings.pollinationsNoLogo}
                        onChange={(e) => updateSetting('pollinationsNoLogo', e.target.checked)}
                        className="sr-only"
                      />
                      <div className={`w-4 h-4 rounded border-2 transition-all duration-300 flex items-center justify-center ${
                        settings.pollinationsNoLogo ? 'border-accent-primary bg-accent-primary' : 'border-gray-500'
                      }`}>
                        {settings.pollinationsNoLogo && (
                          <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      <div>
                        <div className="text-xs font-medium text-white">No Logo</div>
                        <div className="text-xs text-gray-400">Remove watermark</div>
                      </div>
                    </label>

                    <label className="flex items-center gap-2 p-3 rounded-lg border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300">
                      <input
                        type="checkbox"
                        checked={settings.pollinationsEnhance}
                        onChange={(e) => updateSetting('pollinationsEnhance', e.target.checked)}
                        className="sr-only"
                      />
                      <div className={`w-4 h-4 rounded border-2 transition-all duration-300 flex items-center justify-center ${
                        settings.pollinationsEnhance ? 'border-accent-primary bg-accent-primary' : 'border-gray-500'
                      }`}>
                        {settings.pollinationsEnhance && (
                          <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      <div>
                        <div className="text-xs font-medium text-white flex items-center gap-1">
                          <Zap className="w-3 h-3" />
                          Enhance
                        </div>
                        <div className="text-xs text-gray-400">Improve prompts</div>
                      </div>
                    </label>

                    <label className="flex items-center gap-2 p-3 rounded-lg border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300">
                      <input
                        type="checkbox"
                        checked={settings.pollinationsSafe}
                        onChange={(e) => updateSetting('pollinationsSafe', e.target.checked)}
                        className="sr-only"
                      />
                      <div className={`w-4 h-4 rounded border-2 transition-all duration-300 flex items-center justify-center ${
                        settings.pollinationsSafe ? 'border-accent-primary bg-accent-primary' : 'border-gray-500'
                      }`}>
                        {settings.pollinationsSafe && (
                          <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      <div>
                        <div className="text-xs font-medium text-white flex items-center gap-1">
                          <Shield className="w-3 h-3" />
                          Safe Mode
                        </div>
                        <div className="text-xs text-gray-400">Content filtering</div>
                      </div>
                    </label>

                    <label className="flex items-center gap-2 p-3 rounded-lg border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300">
                      <input
                        type="checkbox"
                        checked={settings.pollinationsPrivate}
                        onChange={(e) => updateSetting('pollinationsPrivate', e.target.checked)}
                        className="sr-only"
                      />
                      <div className={`w-4 h-4 rounded border-2 transition-all duration-300 flex items-center justify-center ${
                        settings.pollinationsPrivate ? 'border-accent-primary bg-accent-primary' : 'border-gray-500'
                      }`}>
                        {settings.pollinationsPrivate && (
                          <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      <div>
                        <div className="text-xs font-medium text-white flex items-center gap-1">
                          {settings.pollinationsPrivate ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                          Private
                        </div>
                        <div className="text-xs text-gray-400">Private generation</div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* ImageFX Settings (if selected) */}
          {settings.aiService === 'imagefx' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  ImageFX Auth Token
                </label>
                <input
                  type="password"
                  value={settings.imagefxAuth || ''}
                  onChange={(e) => updateSetting('imagefxAuth', e.target.value)}
                  placeholder="Enter your ImageFX authentication token"
                  className="input-primary w-full"
                />
                <p className="text-xs text-gray-400 mt-1">
                  Required for ImageFX service. See documentation for setup.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  ImageFX Model
                </label>
                <select
                  value={settings.imagefxModel || 'IMAGEN_4'}
                  onChange={(e) => updateSetting('imagefxModel', e.target.value)}
                  className="input-primary w-full"
                >
                  {IMAGEFX_MODELS.map((model) => (
                    <option key={model} value={model}>
                      {model.replace(/_/g, ' ')}
                    </option>
                  ))}
                </select>
                <p className="text-xs text-gray-400 mt-1">
                  Choose the ImageFX model for generation.
                </p>
              </div>

              {/* ImageFX Advanced Settings */}
              <div>
                <label className="text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2">
                  <div className="w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full" />
                  Advanced Options
                </label>
                <div className="space-y-4">
                  {/* Seed */}
                  <div>
                    <label className="text-xs font-medium text-gray-300 mb-2 flex items-center gap-2">
                      <Shuffle className="w-3 h-3" />
                      Seed (optional)
                    </label>
                    <input
                      type="number"
                      value={settings.imagefxSeed || ''}
                      onChange={(e) => updateSetting('imagefxSeed', e.target.value ? parseInt(e.target.value) : undefined)}
                      placeholder="Seed for reference image"
                      className="input-primary w-full text-sm"
                      min="0"
                    />
                  </div>

                  {/* Image Count */}
                  <div>
                    <label className="block text-xs font-medium text-gray-300 mb-2">
                      Number of Images
                    </label>
                    <select
                      value={settings.imagefxCount}
                      onChange={(e) => updateSetting('imagefxCount', parseInt(e.target.value))}
                      className="input-primary w-full text-sm"
                    >
                      <option value={1}>1 image</option>
                      <option value={2}>2 images</option>
                      <option value={3}>3 images</option>
                      <option value={4}>4 images</option>
                      <option value={5}>5 images</option>
                      <option value={6}>6 images</option>
                      <option value={7}>7 images</option>
                      <option value={8}>8 images</option>
                    </select>
                    <p className="text-xs text-gray-400 mt-1">
                      ImageFX supports generating 1-8 images per request.
                    </p>
                  </div>
                </div>
              </div>

              {!settings.imagefxAuth && (
                <div className="flex items-start gap-2 p-3 bg-yellow-900/20 border border-yellow-600/30 rounded-lg">
                  <AlertCircle className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <div className="text-xs text-yellow-200">
                    <p className="font-medium mb-1">Authentication Required</p>
                    <p>ImageFX requires an authentication token to generate images. Please add your token above.</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Mage.space Warning (if selected) */}
          {settings.aiService === 'mage' && (
            <div className="space-y-4">
              <div className="flex items-start gap-2 p-4 bg-red-900/20 border border-red-600/30 rounded-lg">
                <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-red-200">
                  <p className="font-medium mb-2">⚠️ Authentication Required</p>
                  <p className="mb-2">
                    <strong>Mage.space requires user login</strong> for image generation. This scraping approach cannot bypass authentication requirements.
                  </p>
                  <p className="mb-2">
                    The site uses advanced anti-bot measures including:
                  </p>
                  <ul className="list-disc list-inside text-xs space-y-1 mb-2">
                    <li>reCAPTCHA Enterprise</li>
                    <li>Firebase App Check</li>
                    <li>Cloudflare protection</li>
                  </ul>
                  <p className="text-xs">
                    <strong>Recommendation:</strong> Use Pollinations.ai instead - it offers truly unlimited free generation without authentication.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-2 p-3 bg-blue-900/20 border border-blue-600/30 rounded-lg">
                <AlertCircle className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                <div className="text-xs text-blue-200">
                  <p className="font-medium mb-1">Legal Notice</p>
                  <p>Web scraping may violate terms of service. This implementation is for educational purposes only.</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Sidebar
