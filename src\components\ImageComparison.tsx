import React, { useState, useEffect } from 'react'
import { X, Download, Trash2, Edit3, Plus, Minus, RotateCcw } from 'lucide-react'
import { GeneratedImage } from '../types'

interface ImageComparisonProps {
  images: GeneratedImage[]
  isOpen: boolean
  onClose: () => void
  onDownload: (image: GeneratedImage) => void
  onDelete: (image: GeneratedImage) => void
  onEdit?: (image: GeneratedImage) => void
}

const ImageComparison: React.FC<ImageComparisonProps> = ({
  images,
  isOpen,
  onClose,
  onDownload,
  onDelete,
  onEdit
}) => {
  const [selectedImages, setSelectedImages] = useState<GeneratedImage[]>([])
  const [availableImages, setAvailableImages] = useState<GeneratedImage[]>([])
  const [zoom, setZoom] = useState(1)
  const [panX, setPanX] = useState(0)
  const [panY, setPanY] = useState(0)

  // Initialize with first two images when opened
  useEffect(() => {
    if (isOpen && images.length > 0) {
      const nonLoadingImages = images.filter(img => !img.isLoading)
      setAvailableImages(nonLoadingImages)
      
      if (nonLoadingImages.length >= 2) {
        setSelectedImages([nonLoadingImages[0], nonLoadingImages[1]])
      } else if (nonLoadingImages.length === 1) {
        setSelectedImages([nonLoadingImages[0]])
      } else {
        setSelectedImages([])
      }
      
      setZoom(1)
      setPanX(0)
      setPanY(0)
    }
  }, [isOpen, images])

  // Close on Escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  const addImageToComparison = (image: GeneratedImage) => {
    if (selectedImages.length < 4 && !selectedImages.find(img => img.id === image.id)) {
      setSelectedImages(prev => [...prev, image])
    }
  }

  const removeImageFromComparison = (imageId: string) => {
    setSelectedImages(prev => prev.filter(img => img.id !== imageId))
  }



  const resetView = () => {
    setZoom(1)
    setPanX(0)
    setPanY(0)
  }

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-95 flex flex-col z-50">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-700 bg-gray-900">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-semibold text-white">Image Comparison</h2>
          <span className="text-sm text-gray-400">
            {selectedImages.length} of 4 images selected
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Zoom Controls */}
          <div className="flex items-center gap-2 bg-gray-800 rounded-lg p-2">
            <button
              onClick={() => setZoom(prev => Math.max(0.25, prev - 0.25))}
              className="p-1 text-gray-400 hover:text-white transition-colors"
              title="Zoom Out"
            >
              <Minus size={16} />
            </button>
            <span className="text-sm text-gray-300 min-w-[60px] text-center">
              {Math.round(zoom * 100)}%
            </span>
            <button
              onClick={() => setZoom(prev => Math.min(3, prev + 0.25))}
              className="p-1 text-gray-400 hover:text-white transition-colors"
              title="Zoom In"
            >
              <Plus size={16} />
            </button>
            <button
              onClick={resetView}
              className="p-1 text-gray-400 hover:text-white transition-colors ml-2"
              title="Reset View"
            >
              <RotateCcw size={16} />
            </button>
          </div>
          
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-white transition-colors"
          >
            <X size={20} />
          </button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Image Selection Sidebar */}
        <div className="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
          <div className="p-4 border-b border-gray-700">
            <h3 className="text-lg font-medium text-white mb-2">Available Images</h3>
            <p className="text-sm text-gray-400">
              Click to add images to comparison (max 4)
            </p>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4">
            <div className="grid grid-cols-2 gap-3">
              {availableImages.map((image) => {
                const isSelected = selectedImages.find(img => img.id === image.id)
                return (
                  <div
                    key={image.id}
                    className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                      isSelected
                        ? 'border-accent-primary shadow-glow-sm'
                        : 'border-transparent hover:border-gray-600'
                    }`}
                    onClick={() => {
                      if (isSelected) {
                        removeImageFromComparison(image.id)
                      } else {
                        addImageToComparison(image)
                      }
                    }}
                  >
                    <img
                      src={image.url}
                      alt={image.prompt}
                      className="w-full aspect-square object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center">
                      {isSelected ? (
                        <div className="bg-accent-primary text-white rounded-full p-2">
                          <Minus size={16} />
                        </div>
                      ) : (
                        <div className="bg-white text-gray-900 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Plus size={16} />
                        </div>
                      )}
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2">
                      <p className="text-xs text-white truncate">{image.prompt}</p>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Comparison View */}
        <div className="flex-1 flex flex-col">
          {selectedImages.length === 0 ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="w-20 h-20 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Plus className="w-10 h-10 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">No Images Selected</h3>
                <p className="text-gray-400">
                  Select images from the sidebar to start comparing
                </p>
              </div>
            </div>
          ) : (
            <>
              {/* Comparison Grid */}
              <div className="flex-1 p-4">
                <div className={`grid gap-4 h-full ${
                  selectedImages.length === 1 ? 'grid-cols-1' :
                  selectedImages.length === 2 ? 'grid-cols-2' :
                  selectedImages.length === 3 ? 'grid-cols-3' :
                  'grid-cols-2 grid-rows-2'
                }`}>
                  {selectedImages.map((image) => (
                    <div key={image.id} className="relative bg-gray-800 rounded-lg overflow-hidden group">
                      {/* Image */}
                      <div className="relative h-full overflow-hidden">
                        <img
                          src={image.url}
                          alt={image.prompt}
                          className="w-full h-full object-contain transition-transform duration-200"
                          style={{
                            transform: `scale(${zoom}) translate(${panX}px, ${panY}px)`,
                          }}
                          draggable={false}
                        />
                        
                        {/* Action buttons overlay */}
                        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-2">
                          {onEdit && (
                            <button
                              onClick={() => onEdit(image)}
                              className="p-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                              title="Edit"
                            >
                              <Edit3 size={16} />
                            </button>
                          )}
                          <button
                            onClick={() => onDownload(image)}
                            className="p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                            title="Download"
                          >
                            <Download size={16} />
                          </button>
                          <button
                            onClick={() => onDelete(image)}
                            className="p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                            title="Delete"
                          >
                            <Trash2 size={16} />
                          </button>
                          <button
                            onClick={() => removeImageFromComparison(image.id)}
                            className="p-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                            title="Remove from comparison"
                          >
                            <X size={16} />
                          </button>
                        </div>
                      </div>
                      
                      {/* Image info */}
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/80 to-transparent p-3">
                        <p className="text-sm text-white font-medium mb-1 line-clamp-2">
                          {image.prompt}
                        </p>
                        <div className="flex items-center justify-between text-xs text-gray-300">
                          <span className="capitalize">{image.service}</span>
                          <span>{formatTimestamp(image.timestamp)}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default ImageComparison
