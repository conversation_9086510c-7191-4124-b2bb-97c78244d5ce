import React, { useState, useEffect, useRef } from 'react'
import { FolderPlus, Folder, Check, Loader2 } from 'lucide-react'
import { ImageCollection, GeneratedImage } from '../types'

interface ImageContextMenuProps {
  isOpen: boolean
  position: { x: number; y: number }
  image: GeneratedImage | null
  collections: ImageCollection[]
  onClose: () => void
  onAddToCollection: (collectionId: string, imageId: string) => void
  onCreateNewCollection: (imagesToAdd: string[]) => void
}

const ImageContextMenu: React.FC<ImageContextMenuProps> = ({
  isOpen,
  position,
  image,
  collections,
  onClose,
  onAddToCollection,
  onCreateNewCollection
}) => {
  const menuRef = useRef<HTMLDivElement>(null)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  if (!isOpen || !image) return null

  const handleAddToCollection = (collectionId: string) => {
    if (isLoading) return
    setIsLoading(true)
    onAddToCollection(collectionId, image.id)
    // Reset loading state after a short delay for visual feedback
    setTimeout(() => {
      setIsLoading(false)
      onClose()
    }, 300)
  }

  const handleCreateNew = () => {
    if (isLoading) return
    setIsLoading(true)
    onCreateNewCollection([image.id])
    // Reset loading state after a short delay for visual feedback
    setTimeout(() => {
      setIsLoading(false)
      onClose()
    }, 300)
  }

  const isImageInCollection = (collection: ImageCollection) => {
    return collection.imageIds.includes(image.id)
  }

  // Adjust position to keep menu within viewport with better edge handling
  const menuWidth = 250
  const menuHeight = Math.min(300, collections.length * 40 + 120) // Estimate height based on collections

  const adjustedPosition = {
    x: position.x + menuWidth > window.innerWidth
      ? Math.max(10, position.x - menuWidth) // Show on left if no space on right
      : position.x,
    y: position.y + menuHeight > window.innerHeight
      ? Math.max(10, position.y - menuHeight) // Show above if no space below
      : position.y
  }

  return (
    <div
      ref={menuRef}
      className="fixed z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-xl py-2 min-w-[200px]"
      style={{
        left: adjustedPosition.x,
        top: adjustedPosition.y,
      }}
    >
      <div className="px-3 py-2 border-b border-gray-600">
        <p className="text-sm font-medium text-white">Add to Collection</p>
        <p className="text-xs text-gray-400 truncate">{image.prompt}</p>
      </div>

      <div className="max-h-60 overflow-y-auto">
        {collections.length > 0 ? (
          collections.map((collection) => {
            const isInCollection = isImageInCollection(collection)
            return (
              <button
                key={collection.id}
                onClick={() => !isInCollection && !isLoading && handleAddToCollection(collection.id)}
                disabled={isInCollection || isLoading}
                className={`w-full px-3 py-2 text-left hover:bg-gray-700 transition-colors flex items-center gap-2 ${
                  isInCollection || isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                }`}
              >
                <div
                  className="w-3 h-3 rounded-full flex-shrink-0"
                  style={{ backgroundColor: collection.color || '#3b82f6' }}
                />
                <Folder className="w-4 h-4 text-gray-400 flex-shrink-0" />
                <span className="text-sm text-white truncate flex-1">{collection.name}</span>
                {isInCollection && (
                  <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
                )}
              </button>
            )
          })
        ) : (
          <div className="px-3 py-4 text-center">
            <p className="text-sm text-gray-400">No collections yet</p>
          </div>
        )}
      </div>

      <div className="border-t border-gray-600 mt-2">
        <button
          onClick={handleCreateNew}
          disabled={isLoading}
          className={`w-full px-3 py-2 text-left hover:bg-gray-700 transition-colors flex items-center gap-2 text-accent-primary ${
            isLoading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {isLoading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <FolderPlus className="w-4 h-4" />
          )}
          <span className="text-sm">
            {isLoading ? 'Creating...' : 'Create New Collection'}
          </span>
        </button>
      </div>
    </div>
  )
}

export default ImageContextMenu
