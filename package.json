{"name": "imagen-ai-generator", "version": "1.0.0", "description": "Minimal AI Image Generator desktop app with Pollinations AI and ImageFX integration", "main": "dist/main/main.js", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "wait-on http://localhost:5173 && cross-env NODE_ENV=development electron .", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.main.json", "build:preload": "tsc -p tsconfig.preload.json", "package": "npm run build && electron-builder", "package:win": "npm run build && electron-builder --win", "package:mac": "npm run build && electron-builder --mac", "package:linux": "npm run build && electron-builder --linux"}, "keywords": ["electron", "react", "typescript", "ai", "image-generator"], "author": "AI Image Generator", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^28.0.0", "electron-builder": "^24.9.1", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "vite": "^5.0.8", "wait-on": "^7.2.0"}, "dependencies": {"@playwright/test": "^1.54.1", "@rohitaryal/imagefx-api": "^1.0.0", "axios": "^1.6.2", "lucide-react": "^0.294.0", "playwright": "^1.54.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "build": {"appId": "com.imagen.ai-generator", "productName": "Imagen AI Generator", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.graphics-design"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}