import React, { useState, useRef, useEffect, useCallback } from 'react'
import { X, RotateCw, Crop, Palette, Download, Undo, Redo, ZoomIn, ZoomOut, Type, Brush, Square, Circle, Sliders, Filter, Maximize2 } from 'lucide-react'
import { GeneratedImage } from '../types'

interface ImageEditorProps {
  image: GeneratedImage | null
  isOpen: boolean
  onClose: () => void
  onSave: (editedImageData: string, editType: string, editParams: Record<string, any>) => void
}

interface EditState {
  brightness: number
  contrast: number
  saturation: number
  hue: number
  blur: number
  rotation: number
  flipX: boolean
  flipY: boolean
  cropX: number
  cropY: number
  cropWidth: number
  cropHeight: number
  zoom: number
  panX: number
  panY: number
  // Advanced color adjustments
  shadows: number
  highlights: number
  temperature: number
  tint: number
  exposure: number
  // Text overlay
  textOverlays: TextOverlay[]
  // Drawing
  drawings: Drawing[]
}

interface TextOverlay {
  id: string
  text: string
  x: number
  y: number
  fontSize: number
  fontFamily: string
  color: string
  bold: boolean
  italic: boolean
}

interface Drawing {
  id: string
  type: 'brush' | 'rectangle' | 'circle' | 'line'
  points: { x: number; y: number }[]
  color: string
  strokeWidth: number
  opacity?: number
  fill?: boolean
}

const defaultEditState: EditState = {
  brightness: 100,
  contrast: 100,
  saturation: 100,
  hue: 0,
  blur: 0,
  rotation: 0,
  flipX: false,
  flipY: false,
  cropX: 0,
  cropY: 0,
  cropWidth: 100,
  cropHeight: 100,
  zoom: 1,
  panX: 0,
  panY: 0,
  shadows: 0,
  highlights: 0,
  temperature: 0,
  tint: 0,
  exposure: 0,
  textOverlays: [],
  drawings: [],
}

const ImageEditor: React.FC<ImageEditorProps> = ({ image, isOpen, onClose, onSave }) => {
  const [editState, setEditState] = useState<EditState>(defaultEditState)
  const [history, setHistory] = useState<EditState[]>([defaultEditState])
  const [historyIndex, setHistoryIndex] = useState(0)
  const [activeTab, setActiveTab] = useState<'adjust' | 'advanced' | 'filters' | 'transform' | 'crop' | 'text' | 'draw' | 'resize'>('adjust')
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [cropMode, setCropMode] = useState(false)
  const [cropSelection, setCropSelection] = useState({ x: 0, y: 0, width: 0, height: 0 })
  const [selectedTextId, setSelectedTextId] = useState<string | null>(null)
  const [drawingMode, setDrawingMode] = useState<'brush' | 'rectangle' | 'circle' | 'line' | null>(null)
  const [currentDrawing, setCurrentDrawing] = useState<Drawing | null>(null)
  const [drawColor, setDrawColor] = useState('#ffffff')
  const [strokeWidth, setStrokeWidth] = useState(3)
  const [drawOpacity, setDrawOpacity] = useState(1)
  const [colorPickerExpanded, setColorPickerExpanded] = useState(false)
  const [recentColors, setRecentColors] = useState<string[]>(['#ff0000', '#ff8000', '#ffff00', '#80ff00', '#00ff00', '#00ff80', '#00ffff', '#0080ff', '#0000ff', '#8000ff', '#ff00ff', '#ff0080', '#ffffff', '#c0c0c0', '#808080', '#000000'])
  const [customRgb, setCustomRgb] = useState(() => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec('#ffffff')
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 255, g: 255, b: 255 }
  })
  
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Color utility functions
  const hexToRgb = useCallback((hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 0, g: 0, b: 0 }
  }, [])

  const rgbToHex = useCallback((r: number, g: number, b: number) => {
    const toHex = (n: number) => {
      const hex = Math.max(0, Math.min(255, Math.round(n))).toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`
  }, [])

  const isValidHexColor = useCallback((color: string) => {
    return /^#[0-9A-F]{6}$/i.test(color)
  }, [])

  const addToRecentColors = useCallback((color: string) => {
    if (isValidHexColor(color)) {
      setRecentColors(prev => {
        const filtered = prev.filter(c => c !== color)
        return [color, ...filtered].slice(0, 16)
      })
    }
  }, [isValidHexColor])

  // Preset filters
  const presetFilters = [
    { name: 'Original', values: { brightness: 100, contrast: 100, saturation: 100, hue: 0, blur: 0 } },
    { name: 'Vintage', values: { brightness: 110, contrast: 120, saturation: 80, hue: 10, blur: 0.5 } },
    { name: 'Sepia', values: { brightness: 110, contrast: 110, saturation: 60, hue: 30, blur: 0 } },
    { name: 'Black & White', values: { brightness: 100, contrast: 120, saturation: 0, hue: 0, blur: 0 } },
    { name: 'Cool', values: { brightness: 105, contrast: 110, saturation: 120, hue: -10, blur: 0 } },
    { name: 'Warm', values: { brightness: 110, contrast: 105, saturation: 110, hue: 15, blur: 0 } },
    { name: 'Dramatic', values: { brightness: 95, contrast: 140, saturation: 130, hue: 0, blur: 0 } },
    { name: 'Soft', values: { brightness: 115, contrast: 90, saturation: 95, hue: 5, blur: 1 } },
  ]

  // Reset state when image changes
  useEffect(() => {
    if (image) {
      const newState = { ...defaultEditState }
      setEditState(newState)
      setHistory([newState])
      setHistoryIndex(0)
    }
  }, [image])

  // Apply filters to canvas
  const applyFilters = useCallback(() => {
    if (!canvasRef.current || !imageRef.current || !image) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const img = imageRef.current
    
    // Set canvas size
    canvas.width = img.naturalWidth
    canvas.height = img.naturalHeight

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Apply transformations
    ctx.save()
    
    // Move to center for rotation
    ctx.translate(canvas.width / 2, canvas.height / 2)
    
    // Apply rotation
    ctx.rotate((editState.rotation * Math.PI) / 180)
    
    // Apply flip
    ctx.scale(editState.flipX ? -1 : 1, editState.flipY ? -1 : 1)
    
    // Apply filters
    const filters = [
      `brightness(${editState.brightness}%)`,
      `contrast(${editState.contrast}%)`,
      `saturate(${editState.saturation}%)`,
      `hue-rotate(${editState.hue}deg)`,
      `blur(${editState.blur}px)`,
    ]
    ctx.filter = filters.join(' ')
    
    // Draw image
    ctx.drawImage(img, -canvas.width / 2, -canvas.height / 2, canvas.width, canvas.height)

    ctx.restore()

    // Draw text overlays
    editState.textOverlays.forEach((overlay) => {
      ctx.save()
      ctx.font = `${overlay.bold ? 'bold' : ''} ${overlay.italic ? 'italic' : ''} ${overlay.fontSize}px ${overlay.fontFamily}`
      ctx.fillStyle = overlay.color
      ctx.strokeStyle = 'rgba(0,0,0,0.8)'
      ctx.lineWidth = 2

      const x = (overlay.x / 100) * canvas.width
      const y = (overlay.y / 100) * canvas.height

      ctx.strokeText(overlay.text, x, y)
      ctx.fillText(overlay.text, x, y)
      ctx.restore()
    })

    // Draw drawings
    editState.drawings.forEach((drawing) => {
      if (drawing.points.length === 0) return

      ctx.save()
      ctx.globalAlpha = drawing.opacity || 1
      ctx.strokeStyle = drawing.color
      ctx.lineWidth = drawing.strokeWidth
      ctx.lineCap = 'round'
      ctx.lineJoin = 'round'

      if (drawing.type === 'brush') {
        ctx.beginPath()
        ctx.moveTo(drawing.points[0].x, drawing.points[0].y)
        drawing.points.forEach((point) => {
          ctx.lineTo(point.x, point.y)
        })
        ctx.stroke()
      } else if (drawing.type === 'rectangle') {
        const start = drawing.points[0]
        const end = drawing.points[drawing.points.length - 1]
        const width = end.x - start.x
        const height = end.y - start.y

        if (drawing.fill) {
          ctx.fillStyle = drawing.color
          ctx.fillRect(start.x, start.y, width, height)
        } else {
          ctx.strokeRect(start.x, start.y, width, height)
        }
      } else if (drawing.type === 'circle') {
        const start = drawing.points[0]
        const end = drawing.points[drawing.points.length - 1]
        const radius = Math.sqrt(Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2))

        ctx.beginPath()
        ctx.arc(start.x, start.y, radius, 0, 2 * Math.PI)

        if (drawing.fill) {
          ctx.fillStyle = drawing.color
          ctx.fill()
        } else {
          ctx.stroke()
        }
      }
      ctx.restore()
    })

    // Draw current drawing in progress
    if (currentDrawing && currentDrawing.points.length > 0) {
      ctx.save()
      ctx.globalAlpha = currentDrawing.opacity || 1
      ctx.strokeStyle = currentDrawing.color
      ctx.lineWidth = currentDrawing.strokeWidth
      ctx.lineCap = 'round'
      ctx.lineJoin = 'round'

      if (currentDrawing.type === 'brush') {
        ctx.beginPath()
        ctx.moveTo(currentDrawing.points[0].x, currentDrawing.points[0].y)
        currentDrawing.points.forEach((point) => {
          ctx.lineTo(point.x, point.y)
        })
        ctx.stroke()
      } else if (currentDrawing.type === 'rectangle') {
        const start = currentDrawing.points[0]
        const end = currentDrawing.points[currentDrawing.points.length - 1]
        const width = end.x - start.x
        const height = end.y - start.y
        ctx.strokeRect(start.x, start.y, width, height)
      } else if (currentDrawing.type === 'circle') {
        const start = currentDrawing.points[0]
        const end = currentDrawing.points[currentDrawing.points.length - 1]
        const radius = Math.sqrt(Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2))
        ctx.beginPath()
        ctx.arc(start.x, start.y, radius, 0, 2 * Math.PI)
        ctx.stroke()
      }
      ctx.restore()
    }
  }, [editState, image])

  // Load image and apply filters
  useEffect(() => {
    if (!image || !imageRef.current) return

    const img = imageRef.current
    const handleLoad = () => applyFilters()

    img.addEventListener('load', handleLoad)
    img.src = image.url

    return () => {
      img.removeEventListener('load', handleLoad)
      img.onload = null
      img.onerror = null
    }
  }, [image, applyFilters])

  // Apply filters when edit state changes
  useEffect(() => {
    applyFilters()
  }, [applyFilters])

  // Update canvas when drawing in progress with throttling for better performance
  useEffect(() => {
    if (currentDrawing) {
      const timeoutId = setTimeout(() => {
        applyFilters()
      }, 8) // ~120fps for smoother drawing
      return () => clearTimeout(timeoutId)
    }
  }, [currentDrawing, applyFilters])

  // Sync RGB values with hex color (only when color changes from external sources)
  useEffect(() => {
    const rgb = hexToRgb(drawColor)
    // Only update if the RGB values are actually different to prevent loops
    if (rgb.r !== customRgb.r || rgb.g !== customRgb.g || rgb.b !== customRgb.b) {
      setCustomRgb(rgb)
    }
  }, [drawColor, customRgb.r, customRgb.g, customRgb.b, hexToRgb])

  const updateEditState = (updates: Partial<EditState>) => {
    const newState = { ...editState, ...updates }
    setEditState(newState)
    
    // Add to history
    const newHistory = history.slice(0, historyIndex + 1)
    newHistory.push(newState)
    setHistory(newHistory)
    setHistoryIndex(newHistory.length - 1)
  }

  const undo = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1
      setHistoryIndex(newIndex)
      setEditState(history[newIndex])
    }
  }

  const redo = () => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1
      setHistoryIndex(newIndex)
      setEditState(history[newIndex])
    }
  }

  const reset = () => {
    const newState = { ...defaultEditState }
    setEditState(newState)
    setHistory([newState])
    setHistoryIndex(0)
  }

  const applyPresetFilter = (preset: typeof presetFilters[0]) => {
    updateEditState(preset.values)
  }

  const addTextOverlay = () => {
    const newText: TextOverlay = {
      id: `text-${Date.now()}`,
      text: 'Sample Text',
      x: 50,
      y: 50,
      fontSize: 24,
      fontFamily: 'Arial',
      color: '#ffffff',
      bold: false,
      italic: false,
    }
    updateEditState({
      textOverlays: [...editState.textOverlays, newText]
    })
    setSelectedTextId(newText.id)
  }

  const updateTextOverlay = (id: string, updates: Partial<TextOverlay>) => {
    const updatedOverlays = editState.textOverlays.map(overlay =>
      overlay.id === id ? { ...overlay, ...updates } : overlay
    )
    updateEditState({ textOverlays: updatedOverlays })
  }

  const deleteTextOverlay = (id: string) => {
    const updatedOverlays = editState.textOverlays.filter(overlay => overlay.id !== id)
    updateEditState({ textOverlays: updatedOverlays })
    if (selectedTextId === id) {
      setSelectedTextId(null)
    }
  }

  const applyCrop = () => {
    if (!canvasRef.current || !imageRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const { x, y, width, height } = cropSelection

    if (width === 0 || height === 0) return

    // Create new canvas with cropped dimensions
    const croppedCanvas = document.createElement('canvas')
    const croppedCtx = croppedCanvas.getContext('2d')
    if (!croppedCtx) return

    croppedCanvas.width = width
    croppedCanvas.height = height

    // Draw cropped portion
    croppedCtx.drawImage(canvas, x, y, width, height, 0, 0, width, height)

    // Update the main canvas
    canvas.width = width
    canvas.height = height
    ctx.clearRect(0, 0, width, height)
    ctx.drawImage(croppedCanvas, 0, 0)

    setCropMode(false)
    setCropSelection({ x: 0, y: 0, width: 0, height: 0 })
  }

  const resizeImage = (scale: number) => {
    if (!canvasRef.current || !imageRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const img = imageRef.current

    // Calculate new dimensions
    const newWidth = Math.round(img.naturalWidth * scale)
    const newHeight = Math.round(img.naturalHeight * scale)

    // Create a temporary canvas to hold the current state
    const tempCanvas = document.createElement('canvas')
    const tempCtx = tempCanvas.getContext('2d')
    if (!tempCtx) return

    tempCanvas.width = canvas.width
    tempCanvas.height = canvas.height
    tempCtx.drawImage(canvas, 0, 0)

    // Resize the main canvas
    canvas.width = newWidth
    canvas.height = newHeight

    // Redraw the image at new size
    ctx.clearRect(0, 0, newWidth, newHeight)
    ctx.drawImage(img, 0, 0, newWidth, newHeight)

    // Reapply all filters and effects
    applyFilters()
  }

  const handleSave = () => {
    if (!canvasRef.current) return

    const canvas = canvasRef.current
    const editedImageData = canvas.toDataURL('image/png')
    
    const editParams = {
      brightness: editState.brightness,
      contrast: editState.contrast,
      saturation: editState.saturation,
      hue: editState.hue,
      blur: editState.blur,
      rotation: editState.rotation,
      flipX: editState.flipX,
      flipY: editState.flipY,
    }

    onSave(editedImageData, 'filters_and_transform', editParams)
    onClose()
  }

  const getCanvasCoordinates = (e: React.MouseEvent) => {
    const canvas = canvasRef.current
    if (!canvas) return { x: 0, y: 0 }

    const canvasRect = canvas.getBoundingClientRect()

    // Get mouse position relative to the canvas
    const mouseX = e.clientX - canvasRect.left
    const mouseY = e.clientY - canvasRect.top

    // Account for canvas scaling and transforms
    const scaleX = canvas.width / canvasRect.width
    const scaleY = canvas.height / canvasRect.height

    return {
      x: mouseX * scaleX,
      y: mouseY * scaleY
    }
  }

  const handleMouseDown = (e: React.MouseEvent) => {
    const rect = containerRef.current?.getBoundingClientRect()
    if (!rect) return

    const containerX = e.clientX - rect.left
    const containerY = e.clientY - rect.top

    if (cropMode) {
      setIsDragging(true)
      setDragStart({ x: containerX, y: containerY })
      setCropSelection({ x: containerX, y: containerY, width: 0, height: 0 })
    } else if (drawingMode && activeTab === 'draw') {
      const canvasCoords = getCanvasCoordinates(e)
      const newDrawing: Drawing = {
        id: `drawing-${Date.now()}`,
        type: drawingMode,
        points: [canvasCoords],
        color: drawColor,
        strokeWidth,
        opacity: drawOpacity,
        fill: false,
      }
      setCurrentDrawing(newDrawing)
      setIsDragging(true)
    } else {
      setIsDragging(true)
      setDragStart({ x: e.clientX, y: e.clientY })
    }
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    const rect = containerRef.current?.getBoundingClientRect()
    if (!rect) return

    const containerX = e.clientX - rect.left
    const containerY = e.clientY - rect.top

    if (isDragging) {
      if (cropMode) {
        const width = containerX - dragStart.x
        const height = containerY - dragStart.y
        setCropSelection({
          x: Math.min(dragStart.x, containerX),
          y: Math.min(dragStart.y, containerY),
          width: Math.abs(width),
          height: Math.abs(height),
        })
      } else if (currentDrawing && drawingMode) {
        const canvasCoords = getCanvasCoordinates(e)
        if (drawingMode === 'brush') {
          // For brush, add all points
          const updatedDrawing = {
            ...currentDrawing,
            points: [...currentDrawing.points, canvasCoords],
          }
          setCurrentDrawing(updatedDrawing)
        } else {
          // For shapes, only update the end point
          const updatedDrawing = {
            ...currentDrawing,
            points: [currentDrawing.points[0], canvasCoords],
          }
          setCurrentDrawing(updatedDrawing)
        }
      } else {
        const deltaX = e.clientX - dragStart.x
        const deltaY = e.clientY - dragStart.y

        updateEditState({
          panX: editState.panX + deltaX,
          panY: editState.panY + deltaY,
        })

        setDragStart({ x: e.clientX, y: e.clientY })
      }
    }
  }

  const handleMouseUp = () => {
    if (currentDrawing && drawingMode) {
      updateEditState({
        drawings: [...editState.drawings, currentDrawing],
      })
      setCurrentDrawing(null)
    }
    setIsDragging(false)
  }

  if (!isOpen || !image) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-lg w-full h-full max-w-7xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">Image Editor</h2>
          <div className="flex items-center gap-2">
            <button
              onClick={undo}
              disabled={historyIndex === 0}
              className="p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
              title="Undo"
            >
              <Undo size={20} />
            </button>
            <button
              onClick={redo}
              disabled={historyIndex === history.length - 1}
              className="p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
              title="Redo"
            >
              <Redo size={20} />
            </button>
            <button
              onClick={reset}
              className="px-3 py-1 text-sm bg-gray-700 hover:bg-gray-600 text-white rounded"
            >
              Reset
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded flex items-center gap-2"
            >
              <Download size={16} />
              Save
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar */}
          <div className="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
            {/* Tabs */}
            <div className="flex flex-wrap border-b border-gray-700">
              {[
                { id: 'adjust', label: 'Basic', icon: Palette },
                { id: 'advanced', label: 'Advanced', icon: Sliders },
                { id: 'filters', label: 'Filters', icon: Filter },
                { id: 'transform', label: 'Transform', icon: RotateCw },
                { id: 'crop', label: 'Crop', icon: Crop },
                { id: 'text', label: 'Text', icon: Type },
                { id: 'draw', label: 'Draw', icon: Brush },
                { id: 'resize', label: 'Resize', icon: Maximize2 },
              ].map(({ id, label, icon: Icon }) => (
                <button
                  key={id}
                  onClick={() => setActiveTab(id as any)}
                  className={`flex-1 min-w-0 p-2 flex flex-col items-center gap-1 text-xs font-medium ${
                    activeTab === id
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-400 hover:text-white hover:bg-gray-700'
                  }`}
                >
                  <Icon size={14} />
                  <span className="truncate">{label}</span>
                </button>
              ))}
            </div>

            {/* Controls */}
            <div className="flex-1 p-4 overflow-y-auto">
              {activeTab === 'adjust' && (
                <div className="space-y-4">
                  {[
                    { key: 'brightness', label: 'Brightness', min: 0, max: 200, step: 1 },
                    { key: 'contrast', label: 'Contrast', min: 0, max: 200, step: 1 },
                    { key: 'saturation', label: 'Saturation', min: 0, max: 200, step: 1 },
                    { key: 'hue', label: 'Hue', min: -180, max: 180, step: 1 },
                    { key: 'blur', label: 'Blur', min: 0, max: 10, step: 0.1 },
                  ].map(({ key, label, min, max, step }) => (
                    <div key={key}>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        {label}: {editState[key as keyof EditState] as number}
                        {key === 'brightness' || key === 'contrast' || key === 'saturation' ? '%' : ''}
                        {key === 'hue' ? '°' : ''}
                        {key === 'blur' ? 'px' : ''}
                      </label>
                      <input
                        type="range"
                        min={min}
                        max={max}
                        step={step}
                        value={editState[key as keyof EditState] as number}
                        onChange={(e) => updateEditState({ [key]: parseFloat(e.target.value) })}
                        className="w-full slider"
                      />
                    </div>
                  ))}
                </div>
              )}

              {activeTab === 'advanced' && (
                <div className="space-y-4">
                  {[
                    { key: 'shadows', label: 'Shadows', min: -100, max: 100, step: 1 },
                    { key: 'highlights', label: 'Highlights', min: -100, max: 100, step: 1 },
                    { key: 'temperature', label: 'Temperature', min: -100, max: 100, step: 1 },
                    { key: 'tint', label: 'Tint', min: -100, max: 100, step: 1 },
                    { key: 'exposure', label: 'Exposure', min: -100, max: 100, step: 1 },
                  ].map(({ key, label, min, max, step }) => (
                    <div key={key}>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        {label}: {editState[key as keyof EditState] as number}
                      </label>
                      <input
                        type="range"
                        min={min}
                        max={max}
                        step={step}
                        value={editState[key as keyof EditState] as number}
                        onChange={(e) => updateEditState({ [key]: parseFloat(e.target.value) })}
                        className="w-full slider"
                      />
                    </div>
                  ))}
                </div>
              )}

              {activeTab === 'filters' && (
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-gray-300 mb-3">Preset Filters</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {presetFilters.map((preset) => (
                      <button
                        key={preset.name}
                        onClick={() => applyPresetFilter(preset)}
                        className="p-3 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm transition-colors"
                      >
                        {preset.name}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'transform' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Rotation: {editState.rotation}°
                    </label>
                    <input
                      type="range"
                      min={-180}
                      max={180}
                      step={1}
                      value={editState.rotation}
                      onChange={(e) => updateEditState({ rotation: parseInt(e.target.value) })}
                      className="w-full slider"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <button
                      onClick={() => updateEditState({ flipX: !editState.flipX })}
                      className={`w-full p-2 rounded text-sm font-medium ${
                        editState.flipX
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      Flip Horizontal
                    </button>
                    <button
                      onClick={() => updateEditState({ flipY: !editState.flipY })}
                      className={`w-full p-2 rounded text-sm font-medium ${
                        editState.flipY
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      Flip Vertical
                    </button>
                  </div>
                </div>
              )}

              {activeTab === 'crop' && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <button
                      onClick={() => setCropMode(!cropMode)}
                      className={`w-full p-3 rounded text-sm font-medium ${
                        cropMode
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      {cropMode ? 'Exit Crop Mode' : 'Enter Crop Mode'}
                    </button>

                    {cropMode && (
                      <button
                        onClick={applyCrop}
                        className="w-full p-2 bg-green-600 hover:bg-green-700 text-white rounded text-sm"
                      >
                        Apply Crop
                      </button>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Zoom: {Math.round(editState.zoom * 100)}%
                    </label>
                    <input
                      type="range"
                      min={0.1}
                      max={3}
                      step={0.1}
                      value={editState.zoom}
                      onChange={(e) => updateEditState({ zoom: parseFloat(e.target.value) })}
                      className="w-full slider"
                    />
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={() => updateEditState({ zoom: Math.min(3, editState.zoom + 0.1) })}
                      className="flex-1 p-2 bg-gray-700 hover:bg-gray-600 text-white rounded flex items-center justify-center gap-2"
                    >
                      <ZoomIn size={16} />
                      Zoom In
                    </button>
                    <button
                      onClick={() => updateEditState({ zoom: Math.max(0.1, editState.zoom - 0.1) })}
                      className="flex-1 p-2 bg-gray-700 hover:bg-gray-600 text-white rounded flex items-center justify-center gap-2"
                    >
                      <ZoomOut size={16} />
                      Zoom Out
                    </button>
                  </div>
                </div>
              )}

              {activeTab === 'text' && (
                <div className="space-y-4">
                  <button
                    onClick={addTextOverlay}
                    className="w-full p-3 bg-blue-600 hover:bg-blue-700 text-white rounded flex items-center justify-center gap-2"
                  >
                    <Type size={16} />
                    Add Text
                  </button>

                  {editState.textOverlays.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-gray-300">Text Overlays</h4>
                      {editState.textOverlays.map((overlay) => (
                        <div key={overlay.id} className="p-3 bg-gray-700 rounded space-y-2">
                          <input
                            type="text"
                            value={overlay.text}
                            onChange={(e) => updateTextOverlay(overlay.id, { text: e.target.value })}
                            className="w-full p-2 bg-gray-600 text-white rounded text-sm"
                            placeholder="Enter text..."
                          />
                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <label className="block text-xs text-gray-400 mb-1">Size</label>
                              <input
                                type="range"
                                min={12}
                                max={72}
                                value={overlay.fontSize}
                                onChange={(e) => updateTextOverlay(overlay.id, { fontSize: parseInt(e.target.value) })}
                                className="w-full slider"
                              />
                            </div>
                            <div>
                              <label className="block text-xs text-gray-400 mb-1">Color</label>
                              <input
                                type="color"
                                value={overlay.color}
                                onChange={(e) => updateTextOverlay(overlay.id, { color: e.target.value })}
                                className="w-full h-8 bg-gray-600 rounded"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <button
                              onClick={() => updateTextOverlay(overlay.id, { bold: !overlay.bold })}
                              className={`flex-1 p-1 rounded text-xs ${
                                overlay.bold ? 'bg-blue-600 text-white' : 'bg-gray-600 text-gray-300'
                              }`}
                            >
                              Bold
                            </button>
                            <button
                              onClick={() => updateTextOverlay(overlay.id, { italic: !overlay.italic })}
                              className={`flex-1 p-1 rounded text-xs ${
                                overlay.italic ? 'bg-blue-600 text-white' : 'bg-gray-600 text-gray-300'
                              }`}
                            >
                              Italic
                            </button>
                            <button
                              onClick={() => deleteTextOverlay(overlay.id)}
                              className="flex-1 p-1 bg-red-600 hover:bg-red-700 text-white rounded text-xs"
                            >
                              Delete
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'draw' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-2">
                    {[
                      { mode: 'brush', label: 'Brush', icon: Brush },
                      { mode: 'rectangle', label: 'Rectangle', icon: Square },
                      { mode: 'circle', label: 'Circle', icon: Circle },
                    ].map(({ mode, label, icon: Icon }) => (
                      <button
                        key={mode}
                        onClick={() => setDrawingMode(drawingMode === mode ? null : mode as any)}
                        className={`p-3 rounded flex flex-col items-center gap-1 text-xs ${
                          drawingMode === mode
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                        }`}
                      >
                        <Icon size={16} />
                        {label}
                      </button>
                    ))}
                  </div>

                  <div className="space-y-3">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <label className="block text-sm font-medium text-gray-300">
                          Color
                        </label>
                        <button
                          onClick={() => {
                            setColorPickerExpanded(!colorPickerExpanded)
                            // Ensure RGB values are synced when switching to advanced mode
                            if (!colorPickerExpanded) {
                              setCustomRgb(hexToRgb(drawColor))
                            }
                          }}
                          className="text-xs text-blue-400 hover:text-blue-300"
                        >
                          {colorPickerExpanded ? 'Simple' : 'Advanced'}
                        </button>
                      </div>

                      {!colorPickerExpanded ? (
                        <input
                          type="color"
                          value={drawColor}
                          onChange={(e) => {
                            setDrawColor(e.target.value)
                            addToRecentColors(e.target.value)
                            setCustomRgb(hexToRgb(e.target.value))
                          }}
                          className="w-full h-10 bg-gray-600 rounded"
                        />
                      ) : (
                        <div className="space-y-3 p-3 bg-gray-700 rounded">
                          {/* Color Swatches */}
                          <div>
                            <label className="block text-xs text-gray-400 mb-2">Quick Colors</label>
                            <div className="grid grid-cols-8 gap-1">
                              {recentColors.slice(0, 16).map((color, index) => (
                                <button
                                  key={index}
                                  onClick={() => {
                                    setDrawColor(color)
                                    addToRecentColors(color)
                                    setCustomRgb(hexToRgb(color))
                                  }}
                                  className={`w-6 h-6 rounded border-2 ${
                                    drawColor === color ? 'border-white' : 'border-gray-500'
                                  }`}
                                  style={{ backgroundColor: color }}
                                />
                              ))}
                            </div>
                          </div>

                          {/* RGB Sliders */}
                          <div className="space-y-2">
                            <label className="block text-xs text-gray-400">RGB Values</label>
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-red-400 w-4">R</span>
                                <input
                                  type="range"
                                  min={0}
                                  max={255}
                                  value={customRgb.r}
                                  onChange={(e) => {
                                    const newValue = parseInt(e.target.value)
                                    const newRgb = { ...customRgb, r: newValue }
                                    setCustomRgb(newRgb)
                                    const newColor = rgbToHex(newRgb.r, newRgb.g, newRgb.b)
                                    if (newColor !== drawColor) {
                                      setDrawColor(newColor)
                                    }
                                  }}
                                  className="flex-1 slider"
                                />
                                <span className="text-xs text-gray-400 w-8">{customRgb.r}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-green-400 w-4">G</span>
                                <input
                                  type="range"
                                  min={0}
                                  max={255}
                                  value={customRgb.g}
                                  onChange={(e) => {
                                    const newValue = parseInt(e.target.value)
                                    const newRgb = { ...customRgb, g: newValue }
                                    setCustomRgb(newRgb)
                                    const newColor = rgbToHex(newRgb.r, newRgb.g, newRgb.b)
                                    if (newColor !== drawColor) {
                                      setDrawColor(newColor)
                                    }
                                  }}
                                  className="flex-1 slider"
                                />
                                <span className="text-xs text-gray-400 w-8">{customRgb.g}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-blue-400 w-4">B</span>
                                <input
                                  type="range"
                                  min={0}
                                  max={255}
                                  value={customRgb.b}
                                  onChange={(e) => {
                                    const newValue = parseInt(e.target.value)
                                    const newRgb = { ...customRgb, b: newValue }
                                    setCustomRgb(newRgb)
                                    const newColor = rgbToHex(newRgb.r, newRgb.g, newRgb.b)
                                    if (newColor !== drawColor) {
                                      setDrawColor(newColor)
                                    }
                                  }}
                                  className="flex-1 slider"
                                />
                                <span className="text-xs text-gray-400 w-8">{customRgb.b}</span>
                              </div>
                            </div>
                          </div>

                          {/* Hex Input */}
                          <div>
                            <label className="block text-xs text-gray-400 mb-1">Hex Color</label>
                            <input
                              type="text"
                              value={drawColor}
                              onChange={(e) => {
                                let value = e.target.value.trim()

                                // Add # if missing
                                if (value && !value.startsWith('#')) {
                                  value = '#' + value
                                }

                                // Allow partial typing and validate complete hex codes
                                if (value === '#' || /^#[0-9A-F]{0,6}$/i.test(value)) {
                                  if (value.length === 7) {
                                    // Complete hex code
                                    setDrawColor(value.toLowerCase())
                                    addToRecentColors(value.toLowerCase())
                                  } else if (value.length >= 1) {
                                    // Partial hex code - just update the input
                                    setDrawColor(value.toLowerCase())
                                  }
                                }
                              }}
                              onBlur={(e) => {
                                const value = e.target.value.trim()
                                if (value.length === 7 && /^#[0-9A-F]{6}$/i.test(value)) {
                                  setDrawColor(value.toLowerCase())
                                  addToRecentColors(value.toLowerCase())
                                } else if (value.length < 7 && value.length > 1) {
                                  // Pad incomplete hex codes
                                  const paddedValue = value + '0'.repeat(7 - value.length)
                                  if (/^#[0-9A-F]{6}$/i.test(paddedValue)) {
                                    setDrawColor(paddedValue.toLowerCase())
                                    addToRecentColors(paddedValue.toLowerCase())
                                  }
                                }
                              }}
                              className="w-full p-1 bg-gray-600 text-white rounded text-xs font-mono"
                              placeholder="#ffffff"
                              maxLength={7}
                            />
                          </div>

                          {/* Color Preview */}
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-gray-400">Preview:</span>
                            <div
                              className="w-8 h-8 rounded border border-gray-500"
                              style={{ backgroundColor: drawColor }}
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Stroke Width: {strokeWidth}px
                      </label>
                      <input
                        type="range"
                        min={1}
                        max={20}
                        value={strokeWidth}
                        onChange={(e) => setStrokeWidth(parseInt(e.target.value))}
                        className="w-full slider"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Opacity: {Math.round(drawOpacity * 100)}%
                      </label>
                      <input
                        type="range"
                        min={0.1}
                        max={1}
                        step={0.1}
                        value={drawOpacity}
                        onChange={(e) => setDrawOpacity(parseFloat(e.target.value))}
                        className="w-full slider"
                      />
                    </div>
                  </div>

                  {editState.drawings.length > 0 && (
                    <button
                      onClick={() => updateEditState({ drawings: [] })}
                      className="w-full p-2 bg-red-600 hover:bg-red-700 text-white rounded text-sm"
                    >
                      Clear All Drawings
                    </button>
                  )}
                </div>
              )}

              {activeTab === 'resize' && (
                <div className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Resize Options
                      </label>
                      <div className="grid grid-cols-2 gap-2">
                        <button
                          onClick={() => resizeImage(0.5)}
                          className="p-2 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm"
                        >
                          50%
                        </button>
                        <button
                          onClick={() => resizeImage(0.75)}
                          className="p-2 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm"
                        >
                          75%
                        </button>
                        <button
                          onClick={() => resizeImage(1.25)}
                          className="p-2 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm"
                        >
                          125%
                        </button>
                        <button
                          onClick={() => resizeImage(2.0)}
                          className="p-2 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm"
                        >
                          200%
                        </button>
                      </div>
                    </div>

                    <div className="p-3 bg-gray-700 rounded">
                      <p className="text-xs text-gray-400 mb-2">Current Size</p>
                      <p className="text-sm text-white">
                        {canvasRef.current?.width || 0} × {canvasRef.current?.height || 0} pixels
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Canvas Area */}
          <div className="flex-1 bg-gray-900 flex items-center justify-center p-4">
            <div
              ref={containerRef}
              className="relative max-w-full max-h-full overflow-hidden rounded-lg bg-gray-800"
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
            >
              <canvas
                ref={canvasRef}
                className="max-w-full max-h-full object-contain cursor-move"
                style={{
                  transform: `scale(${editState.zoom}) translate(${editState.panX}px, ${editState.panY}px)`,
                }}
              />

              {/* Crop Overlay */}
              {cropMode && (
                <div
                  className="absolute border-2 border-white border-dashed bg-black bg-opacity-30"
                  style={{
                    left: cropSelection.x,
                    top: cropSelection.y,
                    width: cropSelection.width,
                    height: cropSelection.height,
                    pointerEvents: 'none',
                  }}
                />
              )}

              {/* Text Overlays */}
              {editState.textOverlays.map((overlay) => (
                <div
                  key={overlay.id}
                  className="absolute pointer-events-none"
                  style={{
                    left: `${overlay.x}%`,
                    top: `${overlay.y}%`,
                    fontSize: `${overlay.fontSize}px`,
                    fontFamily: overlay.fontFamily,
                    color: overlay.color,
                    fontWeight: overlay.bold ? 'bold' : 'normal',
                    fontStyle: overlay.italic ? 'italic' : 'normal',
                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                    transform: `scale(${editState.zoom})`,
                  }}
                >
                  {overlay.text}
                </div>
              ))}

              <img
                ref={imageRef}
                src={image.url}
                alt="Original"
                className="hidden"
                crossOrigin="anonymous"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ImageEditor
